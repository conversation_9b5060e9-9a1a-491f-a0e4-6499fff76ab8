#!/usr/bin/env python3
"""
SupJAV curl_cffi轻量级提取器
使用curl_cffi模拟Chrome TLS指纹绕过反爬虫
"""

import re
import time
import logging
from urllib.parse import urljoin
from lxml import html
from curl_cffi import requests

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavCurlCffiExtractor:
    def __init__(self):
        # 创建curl_cffi会话，模拟Chrome浏览器
        self.session = requests.Session()
        
        # 设置Chrome浏览器指纹
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })
        
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.session.close()
    
    def extract_video_url(self, page_url, max_retries=3):
        """
        从supjav页面提取视频直链
        
        Args:
            page_url (str): supjav页面URL
            max_retries (int): 最大重试次数
            
        Returns:
            dict: 包含视频信息的字典
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"开始提取视频链接 (尝试 {attempt + 1}/{max_retries}): {page_url}")
                
                # 添加随机延迟
                if attempt > 0:
                    time.sleep(2 + attempt)
                
                # 使用curl_cffi发送请求，模拟Chrome
                response = self.session.get(
                    page_url,
                    timeout=30,
                    impersonate="chrome120",  # 模拟Chrome 120
                    allow_redirects=True
                )
                
                # 检查响应状态
                if response.status_code == 403:
                    logger.warning(f"收到403错误 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return None
                elif response.status_code == 503:
                    logger.warning(f"服务暂时不可用 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        return None
                
                response.raise_for_status()
                
                # 检查是否遇到Cloudflare挑战
                page_content = response.text
                
                if "just a moment" in page_content.lower() or "checking your browser" in page_content.lower():
                    logger.warning(f"遇到Cloudflare挑战 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        return None
                
                # 提取视频信息
                result = self._extract_video_info(page_content, page_url)
                
                if result:
                    logger.info("视频信息提取成功")
                    return result
                else:
                    logger.warning(f"未能提取到视频信息 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
            except Exception as e:
                logger.error(f"提取过程中出错 (尝试 {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        logger.error("所有尝试都失败了")
        return None
    
    def _extract_video_info(self, page_content, page_url):
        """从页面内容提取视频信息"""
        try:
            # 解析HTML
            tree = html.fromstring(page_content)
            
            # 提取标题
            title = self._extract_title(tree)
            logger.info(f"视频标题: {title}")
            
            # 提取视频链接
            video_urls = []
            
            # 方法1: 查找video元素
            video_urls.extend(self._extract_from_video_elements(tree))
            
            # 方法2: 从页面源码分析
            video_urls.extend(self._extract_from_page_source(page_content, page_url))
            
            # 方法3: 查找iframe
            video_urls.extend(self._extract_from_iframes(tree, page_url))
            
            # 去重并验证
            unique_urls = list(dict.fromkeys(video_urls))
            valid_urls = []
            
            for url in unique_urls:
                if self._is_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到视频链接: {url}")
            
            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                # 输出页面内容片段用于调试
                logger.debug(f"页面内容片段: {page_content[:1000]}...")
                return None
                
            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }
            
        except Exception as e:
            logger.error(f"提取视频信息时出错: {str(e)}")
            return None
    
    def _extract_title(self, tree):
        """提取视频标题"""
        try:
            # 尝试多种选择器
            selectors = [
                '//h1[@class="entry-title"]/text()',
                '//h1/text()',
                '//title/text()',
                '//*[@class="post-title"]/text()',
                '//*[@class="video-title"]/text()'
            ]
            
            for selector in selectors:
                elements = tree.xpath(selector)
                if elements:
                    title = elements[0].strip()
                    if title:
                        # 清理标题
                        title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                        return title
            
            return "Unknown Title"
            
        except Exception as e:
            logger.debug(f"提取标题失败: {str(e)}")
            return "Unknown Title"
    
    def _extract_from_video_elements(self, tree):
        """从video元素提取链接"""
        urls = []
        try:
            # 查找所有video元素
            video_elements = tree.xpath('//video')
            
            for video in video_elements:
                # 检查src属性
                src = video.get('src')
                if src and self._is_video_url(src):
                    urls.append(src)
                
                # 检查source子元素
                sources = video.xpath('.//source/@src')
                for src in sources:
                    if self._is_video_url(src):
                        urls.append(src)
            
        except Exception as e:
            logger.debug(f"从video元素提取链接失败: {str(e)}")
        
        return urls
    
    def _extract_from_page_source(self, page_source, base_url):
        """从页面源码提取视频链接"""
        urls = []
        
        # 视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'video\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    full_url = urljoin(base_url, match)
                    urls.append(full_url)
        
        return urls
    
    def _extract_from_iframes(self, tree, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        try:
            iframes = tree.xpath('//iframe/@src')
            
            for iframe_src in iframes:
                if any(keyword in iframe_src.lower() for keyword in ['player', 'embed', 'video']):
                    logger.debug(f"发现iframe播放器: {iframe_src}")
                    # 可以进一步获取iframe内容
                    try:
                        iframe_url = urljoin(base_url, iframe_src)
                        iframe_response = self.session.get(
                            iframe_url,
                            timeout=15,
                            impersonate="chrome120"
                        )
                        
                        if iframe_response.status_code == 200:
                            iframe_tree = html.fromstring(iframe_response.text)
                            urls.extend(self._extract_from_video_elements(iframe_tree))
                            urls.extend(self._extract_from_page_source(iframe_response.text, iframe_url))
                            
                    except Exception as e:
                        logger.debug(f"获取iframe内容失败: {str(e)}")
                        continue
                    
        except Exception as e:
            logger.debug(f"从iframe提取失败: {str(e)}")
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = ['video', 'stream', 'play', 'media']
        return any(pattern in url.lower() for pattern in video_patterns)
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]

def main():
    """测试函数"""
    with SupJavCurlCffiExtractor() as extractor:
        test_url = "https://supjav.com/357979.html"
        
        result = extractor.extract_video_url(test_url)
        if result:
            print(f"标题: {result['title']}")
            print(f"最佳链接: {result['best_url']}")
            print(f"所有链接: {result['video_urls']}")
        else:
            print("提取失败")

if __name__ == "__main__":
    main()
