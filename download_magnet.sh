#!/bin/bash
# 磁力链接下载脚本
# 生成时间: 2025-06-30 12:51:10

MAGNET_URL="magnet:?xt=urn:btih:9866D7F7256C520DE0A495F1945EEDDF1D3136B9&dn=embz-325"
DOWNLOAD_DIR="/www/wwwroot/JAVAPI.COM/downloads"
HASH="9866D7F7256C520DE0A495F1945EEDDF1D3136B9"
NAME="embz-325"

echo "🧲 磁力链接下载脚本"
echo "=================================="
echo "📁 名称: $NAME"
echo "🔑 Hash: $HASH"
echo "📂 下载目录: $DOWNLOAD_DIR"
echo "=================================="

# 创建下载目录
mkdir -p "$DOWNLOAD_DIR"

# 方法1: 使用aria2c直接下载
echo "🚀 方法1: aria2c直接下载"
aria2c \
    --dir="$DOWNLOAD_DIR" \
    --max-connection-per-server=16 \
    --max-concurrent-downloads=16 \
    --split=16 \
    --min-split-size=1M \
    --bt-max-peers=100 \
    --seed-time=0 \
    --bt-tracker-connect-timeout=10 \
    --bt-tracker-timeout=10 \
    --follow-torrent=mem \
    --enable-dht=true \
    --bt-enable-lpd=true \
    --enable-peer-exchange=true \
    --continue=true \
    --summary-interval=60 \
    "$MAGNET_URL"

echo "✅ 下载完成!"
echo "📁 文件位置: $DOWNLOAD_DIR"
ls -la "$DOWNLOAD_DIR"
