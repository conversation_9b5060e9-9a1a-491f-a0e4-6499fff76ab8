#!/usr/bin/env python3
"""
SupJAV浏览器自动化视频链接提取器
使用Selenium WebDriver来绕过反爬虫保护
"""

import re
import json
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, WebDriverException
import requests
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavBrowserExtractor:
    def __init__(self, headless=True):
        self.headless = headless
        self.driver = None
        self._setup_driver()
        
    def _setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 添加反检测选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片和CSS加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 尝试使用系统Chrome
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Chrome WebDriver初始化成功")
            
        except Exception as e:
            logger.error(f"WebDriver初始化失败: {str(e)}")
            raise
    
    def extract_video_url(self, page_url):
        """
        从supjav页面提取视频直链
        
        Args:
            page_url (str): supjav页面URL
            
        Returns:
            dict: 包含视频信息的字典
        """
        try:
            logger.info(f"开始提取视频链接: {page_url}")
            
            # 访问页面
            self.driver.get(page_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待额外时间让JavaScript执行
            time.sleep(3)
            
            # 提取标题
            title = self._extract_title()
            logger.info(f"视频标题: {title}")
            
            # 提取视频链接
            video_urls = []
            
            # 方法1: 查找video标签
            video_urls.extend(self._extract_from_video_elements())
            
            # 方法2: 从页面源码中提取
            page_source = self.driver.page_source
            video_urls.extend(self._extract_from_page_source(page_source, page_url))
            
            # 方法3: 执行JavaScript获取播放器信息
            video_urls.extend(self._extract_from_javascript())
            
            # 方法4: 查找iframe
            video_urls.extend(self._extract_from_iframes(page_url))
            
            # 去重并验证
            unique_urls = list(dict.fromkeys(video_urls))
            valid_urls = []
            
            for url in unique_urls:
                if self._validate_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到有效视频链接: {url}")
            
            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                return None
                
            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }
            
        except Exception as e:
            logger.error(f"提取视频链接失败: {str(e)}")
            return None
    
    def _extract_title(self):
        """提取视频标题"""
        try:
            # 尝试多种选择器
            selectors = [
                "h1.entry-title",
                "h1",
                "title",
                ".post-title",
                ".video-title"
            ]
            
            for selector in selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    title = element.text.strip()
                    if title:
                        # 清理标题
                        title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                        return title
                except:
                    continue
            
            # 从页面标题获取
            title = self.driver.title
            title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
            return title if title else "Unknown Title"
            
        except Exception as e:
            logger.debug(f"提取标题失败: {str(e)}")
            return "Unknown Title"
    
    def _extract_from_video_elements(self):
        """从video元素提取链接"""
        urls = []
        try:
            # 查找所有video元素
            video_elements = self.driver.find_elements(By.TAG_NAME, "video")
            
            for video in video_elements:
                # 检查src属性
                src = video.get_attribute("src")
                if src and self._is_video_url(src):
                    urls.append(src)
                
                # 检查source子元素
                sources = video.find_elements(By.TAG_NAME, "source")
                for source in sources:
                    src = source.get_attribute("src")
                    if src and self._is_video_url(src):
                        urls.append(src)
            
        except Exception as e:
            logger.debug(f"从video元素提取链接失败: {str(e)}")
        
        return urls
    
    def _extract_from_page_source(self, page_source, base_url):
        """从页面源码提取视频链接"""
        urls = []
        
        # 视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    full_url = urljoin(base_url, match)
                    urls.append(full_url)
        
        return urls
    
    def _extract_from_javascript(self):
        """通过JavaScript获取播放器信息"""
        urls = []
        
        try:
            # 尝试获取常见播放器的配置
            scripts = [
                "return window.jwplayer ? window.jwplayer().getConfig() : null;",
                "return window.videojs ? window.videojs.getPlayers() : null;",
                "return window.player ? window.player.src() : null;",
                "return document.querySelector('video') ? document.querySelector('video').src : null;"
            ]
            
            for script in scripts:
                try:
                    result = self.driver.execute_script(script)
                    if result:
                        logger.debug(f"JavaScript结果: {result}")
                        # 解析结果中的视频URL
                        if isinstance(result, str) and self._is_video_url(result):
                            urls.append(result)
                        elif isinstance(result, dict):
                            # 从配置对象中提取URL
                            self._extract_urls_from_config(result, urls)
                except Exception as e:
                    logger.debug(f"JavaScript执行失败: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.debug(f"JavaScript提取失败: {str(e)}")
        
        return urls
    
    def _extract_urls_from_config(self, config, urls):
        """从配置对象中提取URL"""
        if isinstance(config, dict):
            for key, value in config.items():
                if key in ['file', 'src', 'url'] and isinstance(value, str) and self._is_video_url(value):
                    urls.append(value)
                elif key == 'sources' and isinstance(value, list):
                    for source in value:
                        if isinstance(source, dict) and 'file' in source:
                            if self._is_video_url(source['file']):
                                urls.append(source['file'])
                        elif isinstance(source, str) and self._is_video_url(source):
                            urls.append(source)
                elif isinstance(value, (dict, list)):
                    self._extract_urls_from_config(value, urls)
    
    def _extract_from_iframes(self, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        try:
            iframes = self.driver.find_elements(By.TAG_NAME, "iframe")
            
            for iframe in iframes:
                src = iframe.get_attribute("src")
                if src and any(keyword in src.lower() for keyword in ['player', 'embed', 'video']):
                    # 切换到iframe
                    try:
                        self.driver.switch_to.frame(iframe)
                        
                        # 在iframe中查找视频元素
                        iframe_urls = self._extract_from_video_elements()
                        urls.extend(iframe_urls)
                        
                        # 切换回主页面
                        self.driver.switch_to.default_content()
                        
                    except Exception as e:
                        logger.debug(f"处理iframe失败: {str(e)}")
                        self.driver.switch_to.default_content()
                        continue
                        
        except Exception as e:
            logger.debug(f"从iframe提取失败: {str(e)}")
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = ['video', 'stream', 'play', 'media']
        return any(pattern in url.lower() for pattern in video_patterns)
    
    def _validate_video_url(self, url):
        """验证视频URL是否有效"""
        try:
            response = requests.head(url, timeout=10, allow_redirects=True)
            
            if response.status_code not in [200, 206]:
                return False
            
            content_type = response.headers.get('Content-Type', '').lower()
            if any(vtype in content_type for vtype in ['video/', 'application/vnd.apple.mpegurl']):
                return True
            
            content_length = response.headers.get('Content-Length')
            if content_length and int(content_length) > 1024 * 1024:
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"验证URL失败: {url}, 错误: {str(e)}")
            return False
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """测试函数"""
    extractor = None
    try:
        extractor = SupJavBrowserExtractor(headless=True)
        
        # 测试URL
        test_url = "https://supjav.com/357979.html"
        
        result = extractor.extract_video_url(test_url)
        if result:
            print(f"标题: {result['title']}")
            print(f"最佳链接: {result['best_url']}")
            print(f"所有链接: {result['video_urls']}")
        else:
            print("提取失败")
            
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        if extractor:
            extractor.close()

if __name__ == "__main__":
    main()
