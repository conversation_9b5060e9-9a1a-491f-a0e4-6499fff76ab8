#!/usr/bin/env python3
"""
SupJAV视频链接提取器
从supjav.com页面提取直接播放链接
"""

import re
import json
import time
import logging
import requests
import cloudscraper
from urllib.parse import urljoin, urlparse
from lxml import html
import base64

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavExtractor:
    def __init__(self):
        # 创建cloudscraper实例来绕过Cloudflare
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'desktop': True
            }
        )
        self.scraper.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        })

        # 添加会话保持
        self.session_cookies = {}

        # 备用User-Agent列表
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        self.current_ua_index = 0
        
    def extract_video_url(self, page_url):
        """
        从supjav页面提取视频直链

        Args:
            page_url (str): supjav页面URL

        Returns:
            dict: 包含视频信息的字典
        """
        max_retries = 3
        response = None
        tree = None

        for attempt in range(max_retries):
            try:
                logger.info(f"开始提取视频链接 (尝试 {attempt + 1}/{max_retries}): {page_url}")

                # 添加随机延迟避免被检测
                if attempt > 0:
                    import time
                    time.sleep(2 + attempt)

                # 获取页面内容
                response = self.scraper.get(page_url, timeout=30)

                # 检查响应状态
                if response.status_code == 403:
                    logger.warning(f"收到403错误，尝试更换User-Agent (尝试 {attempt + 1})")
                    self._rotate_user_agent()
                    continue
                elif response.status_code == 503:
                    logger.warning(f"服务暂时不可用，等待后重试 (尝试 {attempt + 1})")
                    import time
                    time.sleep(5)
                    continue

                response.raise_for_status()

                # 解析HTML
                tree = html.fromstring(response.content)
                break

            except Exception as e:
                logger.warning(f"尝试 {attempt + 1} 失败: {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"所有尝试都失败了: {str(e)}")
                    return None
                continue

        # 检查是否成功获取到页面
        if tree is None or response is None:
            logger.error("无法获取页面内容")
            return None

        try:
            # 提取基本信息
            title = self._extract_title(tree)
            logger.info(f"视频标题: {title}")

            # 尝试多种方法提取视频链接
            video_urls = []

            # 方法1: 查找video标签
            video_urls.extend(self._extract_from_video_tags(tree, page_url))

            # 方法2: 查找JavaScript中的视频URL
            video_urls.extend(self._extract_from_javascript(response.text, page_url))

            # 方法3: 查找iframe嵌入的播放器
            video_urls.extend(self._extract_from_iframes(tree, page_url))

            # 方法4: 查找特定的播放器配置
            video_urls.extend(self._extract_from_player_config(response.text, page_url))

            # 去重并验证链接
            unique_urls = list(dict.fromkeys(video_urls))  # 保持顺序去重
            valid_urls = []

            for url in unique_urls:
                if self._validate_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到有效视频链接: {url}")

            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                return None

            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }

        except Exception as e:
            logger.error(f"提取视频链接失败: {str(e)}")
            return None

    def _rotate_user_agent(self):
        """轮换User-Agent"""
        self.current_ua_index = (self.current_ua_index + 1) % len(self.user_agents)
        new_ua = self.user_agents[self.current_ua_index]
        self.scraper.headers['User-Agent'] = new_ua
        logger.info(f"已更换User-Agent: {new_ua[:50]}...")

    def _extract_title(self, tree):
        """提取视频标题"""
        # 尝试多种选择器
        selectors = [
            '//title/text()',
            '//h1[@class="entry-title"]/text()',
            '//h1/text()',
            '//meta[@property="og:title"]/@content',
            '//meta[@name="title"]/@content'
        ]
        
        for selector in selectors:
            elements = tree.xpath(selector)
            if elements:
                title = elements[0].strip()
                # 清理标题
                title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                return title
        
        return "Unknown Title"
    
    def _extract_from_video_tags(self, tree, base_url):
        """从video标签提取链接"""
        urls = []
        
        # 查找video标签
        video_elements = tree.xpath('//video')
        for video in video_elements:
            # 检查src属性
            src = video.get('src')
            if src:
                urls.append(urljoin(base_url, src))
            
            # 检查source子标签
            sources = video.xpath('.//source/@src')
            for src in sources:
                urls.append(urljoin(base_url, src))
        
        return urls
    
    def _extract_from_javascript(self, html_content, base_url):
        """从JavaScript代码中提取视频链接"""
        urls = []
        
        # 常见的视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'["\']([^"\']*\.avi[^"\']*)["\']',
            r'["\']([^"\']*\.mkv[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'video\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    urls.append(urljoin(base_url, match))
        
        # 查找base64编码的URL
        base64_pattern = r'atob\(["\']([A-Za-z0-9+/=]+)["\']\)'
        base64_matches = re.findall(base64_pattern, html_content)
        for encoded in base64_matches:
            try:
                decoded = base64.b64decode(encoded).decode('utf-8')
                if self._is_video_url(decoded):
                    urls.append(urljoin(base_url, decoded))
            except:
                continue
        
        return urls
    
    def _extract_from_iframes(self, tree, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        iframes = tree.xpath('//iframe/@src')
        for iframe_src in iframes:
            iframe_url = urljoin(base_url, iframe_src)
            
            # 如果iframe指向视频播放器，尝试获取其内容
            if any(domain in iframe_url.lower() for domain in ['player', 'embed', 'video']):
                try:
                    iframe_response = self.scraper.get(iframe_url, timeout=15)
                    iframe_tree = html.fromstring(iframe_response.content)
                    
                    # 从iframe内容中提取视频链接
                    urls.extend(self._extract_from_video_tags(iframe_tree, iframe_url))
                    urls.extend(self._extract_from_javascript(iframe_response.text, iframe_url))
                    
                except Exception as e:
                    logger.debug(f"无法获取iframe内容: {iframe_url}, 错误: {str(e)}")
        
        return urls
    
    def _extract_from_player_config(self, html_content, base_url):
        """从播放器配置中提取视频链接"""
        urls = []
        
        # 查找常见的播放器配置模式
        config_patterns = [
            r'jwplayer\([^)]*\)\.setup\(([^}]+})\)',
            r'videojs\([^)]*,\s*({[^}]+})\)',
            r'player\.setup\(({[^}]+})\)',
            r'new\s+Player\([^,]*,\s*({[^}]+})\)',
        ]
        
        for pattern in config_patterns:
            matches = re.findall(pattern, html_content, re.DOTALL)
            for match in matches:
                try:
                    # 尝试解析JSON配置
                    config = json.loads(match)
                    
                    # 查找视频源
                    if 'file' in config:
                        urls.append(urljoin(base_url, config['file']))
                    
                    if 'sources' in config:
                        for source in config['sources']:
                            if isinstance(source, dict) and 'file' in source:
                                urls.append(urljoin(base_url, source['file']))
                            elif isinstance(source, str):
                                urls.append(urljoin(base_url, source))
                    
                except (json.JSONDecodeError, TypeError):
                    continue
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = [
            r'video',
            r'stream',
            r'play',
            r'media',
            r'content'
        ]
        
        return any(re.search(pattern, url, re.IGNORECASE) for pattern in video_patterns)
    
    def _validate_video_url(self, url):
        """验证视频URL是否有效"""
        try:
            # 发送HEAD请求检查URL
            response = self.scraper.head(url, timeout=10, allow_redirects=True)
            
            # 检查状态码
            if response.status_code not in [200, 206]:
                return False
            
            # 检查Content-Type
            content_type = response.headers.get('Content-Type', '').lower()
            if any(vtype in content_type for vtype in ['video/', 'application/vnd.apple.mpegurl']):
                return True
            
            # 检查Content-Length（视频文件通常较大）
            content_length = response.headers.get('Content-Length')
            if content_length and int(content_length) > 1024 * 1024:  # 大于1MB
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"验证URL失败: {url}, 错误: {str(e)}")
            return False
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]

def main():
    """测试函数"""
    extractor = SupJavExtractor()
    
    # 测试URL
    test_url = "https://supjav.com/357979.html"
    
    result = extractor.extract_video_url(test_url)
    if result:
        print(f"标题: {result['title']}")
        print(f"最佳链接: {result['best_url']}")
        print(f"所有链接: {result['video_urls']}")
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
