#!/usr/bin/env python3
"""
磁力链接转直链工具 - 测试版
"""

import requests
import re
import sys
from urllib.parse import quote

def process_magnet(magnet_url):
    print("🧲 磁力链接转直链工具")
    print("=" * 50)
    
    # 提取信息
    hash_match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
    name_match = re.search(r'dn=([^&]+)', magnet_url)
    
    if hash_match:
        torrent_hash = hash_match.group(1)
        print(f"🔑 Hash: {torrent_hash}")
    
    if name_match:
        from urllib.parse import unquote
        name = unquote(name_match.group(1))
        print(f"📁 名称: {name}")
    
    print(f"📋 磁力链接: {magnet_url}")
    
    # 生成各种在线服务链接
    services = []
    
    # Webtor.io
    webtor_url = f"https://webtor.io/?magnet={quote(magnet_url)}"
    services.append(("Webtor.io", webtor_url, "在线播放和下载"))
    
    # Instant.io
    instant_url = f"https://instant.io/#{magnet_url}"
    services.append(("Instant.io", instant_url, "即时下载和流媒体"))
    
    # BTCache.me
    if hash_match:
        btcache_url = f"https://btcache.me/torrent/{torrent_hash}"
        services.append(("BTCache.me", btcache_url, "Torrent缓存和下载"))
    
    # Seedr.cc
    seedr_url = f"https://www.seedr.cc/?magnet={quote(magnet_url)}"
    services.append(("Seedr.cc", seedr_url, "云端下载服务"))
    
    # Put.io
    putio_url = f"https://put.io/transfers/add?url={quote(magnet_url)}"
    services.append(("Put.io", putio_url, "云存储下载"))
    
    print("\n🎯 可用的在线服务:")
    print("-" * 50)
    
    for i, (name, url, desc) in enumerate(services, 1):
        print(f"{i}. {name}")
        print(f"   🌐 {url}")
        print(f"   📝 {desc}")
        print()
    
    print("📖 使用方法:")
    print("-" * 30)
    print("1. 复制上面的链接到浏览器打开")
    print("2. 注册账号（某些服务需要）")
    print("3. 等待torrent解析完成")
    print("4. 选择要下载的文件")
    print("5. 获取直接下载链接")
    
    # 生成curl命令示例
    print("\n🔧 API调用示例:")
    print("-" * 30)
    print("# 使用webtor.io API:")
    print(f"curl 'https://webtor.io/api/magnet' -d 'magnet={quote(magnet_url)}'")
    
    return services

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python3 magnet_test.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    if not magnet_url.startswith('magnet:'):
        print("❌ 请提供有效的磁力链接")
        sys.exit(1)
    
    process_magnet(magnet_url)