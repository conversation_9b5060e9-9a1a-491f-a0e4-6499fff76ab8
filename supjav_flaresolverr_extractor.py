#!/usr/bin/env python3
"""
SupJAV FlareSolverr提取器
使用FlareSolverr代理服务绕过Cloudflare保护
"""

import re
import json
import time
import logging
import requests
from urllib.parse import urljoin
from lxml import html

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavFlareSolverrExtractor:
    def __init__(self, flaresolverr_url="http://localhost:8191/v1"):
        self.flaresolverr_url = flaresolverr_url
        self.session_id = None
        
    def __enter__(self):
        """上下文管理器入口"""
        self.create_session()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.destroy_session()
    
    def create_session(self):
        """创建FlareSolverr会话"""
        try:
            logger.info("创建FlareSolverr会话...")
            
            payload = {
                "cmd": "sessions.create",
                "session": f"supjav_session_{int(time.time())}"
            }
            
            response = requests.post(self.flaresolverr_url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("status") == "ok":
                self.session_id = result.get("session")
                logger.info(f"FlareSolverr会话创建成功: {self.session_id}")
            else:
                raise Exception(f"创建会话失败: {result}")
                
        except Exception as e:
            logger.error(f"创建FlareSolverr会话失败: {str(e)}")
            raise
    
    def destroy_session(self):
        """销毁FlareSolverr会话"""
        if self.session_id:
            try:
                payload = {
                    "cmd": "sessions.destroy",
                    "session": self.session_id
                }
                
                response = requests.post(self.flaresolverr_url, json=payload, timeout=10)
                logger.info("FlareSolverr会话已销毁")
                
            except Exception as e:
                logger.error(f"销毁会话时出错: {str(e)}")
    
    def extract_video_url(self, page_url, max_retries=3):
        """
        从supjav页面提取视频直链
        
        Args:
            page_url (str): supjav页面URL
            max_retries (int): 最大重试次数
            
        Returns:
            dict: 包含视频信息的字典
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"开始提取视频链接 (尝试 {attempt + 1}/{max_retries}): {page_url}")
                
                # 通过FlareSolverr获取页面
                page_content = self._get_page_via_flaresolverr(page_url)
                
                if not page_content:
                    logger.warning(f"无法获取页面内容 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        return None
                
                # 提取视频信息
                result = self._extract_video_info(page_content, page_url)
                
                if result:
                    logger.info("视频信息提取成功")
                    return result
                else:
                    logger.warning(f"未能提取到视频信息 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
            except Exception as e:
                logger.error(f"提取过程中出错 (尝试 {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        logger.error("所有尝试都失败了")
        return None
    
    def _get_page_via_flaresolverr(self, url):
        """通过FlareSolverr获取页面内容"""
        try:
            payload = {
                "cmd": "request.get",
                "url": url,
                "session": self.session_id,
                "maxTimeout": 60000  # 60秒超时
            }
            
            logger.info("通过FlareSolverr请求页面...")
            response = requests.post(self.flaresolverr_url, json=payload, timeout=70)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get("status") == "ok":
                solution = result.get("solution", {})
                page_content = solution.get("response")
                
                if page_content:
                    logger.info(f"成功获取页面内容，长度: {len(page_content)} 字符")
                    return page_content
                else:
                    logger.error("FlareSolverr返回空内容")
                    return None
            else:
                logger.error(f"FlareSolverr请求失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"通过FlareSolverr获取页面失败: {str(e)}")
            return None
    
    def _extract_video_info(self, page_content, page_url):
        """从页面内容提取视频信息"""
        try:
            # 解析HTML
            tree = html.fromstring(page_content)
            
            # 提取标题
            title = self._extract_title(tree)
            logger.info(f"视频标题: {title}")
            
            # 提取视频链接
            video_urls = []
            
            # 方法1: 查找video元素
            video_urls.extend(self._extract_from_video_elements(tree))
            
            # 方法2: 从页面源码分析
            video_urls.extend(self._extract_from_page_source(page_content, page_url))
            
            # 方法3: 查找iframe
            video_urls.extend(self._extract_from_iframes(tree, page_url))
            
            # 去重并验证
            unique_urls = list(dict.fromkeys(video_urls))
            valid_urls = []
            
            for url in unique_urls:
                if self._is_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到视频链接: {url}")
            
            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                return None
                
            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }
            
        except Exception as e:
            logger.error(f"提取视频信息时出错: {str(e)}")
            return None
    
    def _extract_title(self, tree):
        """提取视频标题"""
        try:
            # 尝试多种选择器
            selectors = [
                '//h1[@class="entry-title"]/text()',
                '//h1/text()',
                '//title/text()',
                '//*[@class="post-title"]/text()',
                '//*[@class="video-title"]/text()'
            ]
            
            for selector in selectors:
                elements = tree.xpath(selector)
                if elements:
                    title = elements[0].strip()
                    if title:
                        # 清理标题
                        title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                        return title
            
            return "Unknown Title"
            
        except Exception as e:
            logger.debug(f"提取标题失败: {str(e)}")
            return "Unknown Title"
    
    def _extract_from_video_elements(self, tree):
        """从video元素提取链接"""
        urls = []
        try:
            # 查找所有video元素
            video_elements = tree.xpath('//video')
            
            for video in video_elements:
                # 检查src属性
                src = video.get('src')
                if src and self._is_video_url(src):
                    urls.append(src)
                
                # 检查source子元素
                sources = video.xpath('.//source/@src')
                for src in sources:
                    if self._is_video_url(src):
                        urls.append(src)
            
        except Exception as e:
            logger.debug(f"从video元素提取链接失败: {str(e)}")
        
        return urls
    
    def _extract_from_page_source(self, page_source, base_url):
        """从页面源码提取视频链接"""
        urls = []
        
        # 视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    full_url = urljoin(base_url, match)
                    urls.append(full_url)
        
        return urls
    
    def _extract_from_iframes(self, tree, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        try:
            iframes = tree.xpath('//iframe/@src')
            
            for iframe_src in iframes:
                if any(keyword in iframe_src.lower() for keyword in ['player', 'embed', 'video']):
                    logger.debug(f"发现iframe播放器: {iframe_src}")
                    # 可以进一步通过FlareSolverr获取iframe内容
                    
        except Exception as e:
            logger.debug(f"从iframe提取失败: {str(e)}")
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = ['video', 'stream', 'play', 'media']
        return any(pattern in url.lower() for pattern in video_patterns)
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]

def main():
    """测试函数"""
    # 首先检查FlareSolverr是否运行
    try:
        # FlareSolverr对GET请求返回405，这是正常的
        response = requests.get("http://localhost:8191/v1", timeout=5)
        if response.status_code not in [200, 405]:
            print("错误: FlareSolverr服务响应异常")
            print("请检查FlareSolverr服务状态")
            return
    except requests.exceptions.RequestException:
        print("错误: 无法连接到FlareSolverr服务")
        print("请先启动FlareSolverr: docker-compose -f docker-compose.flaresolverr.yml up -d")
        return
    
    with SupJavFlareSolverrExtractor() as extractor:
        test_url = "https://supjav.com/357979.html"
        
        result = extractor.extract_video_url(test_url)
        if result:
            print(f"标题: {result['title']}")
            print(f"最佳链接: {result['best_url']}")
            print(f"所有链接: {result['video_urls']}")
        else:
            print("提取失败")

if __name__ == "__main__":
    main()
