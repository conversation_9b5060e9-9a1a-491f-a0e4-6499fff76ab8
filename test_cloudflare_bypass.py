#!/usr/bin/env python3
"""
测试Cloudflare绕过功能
"""

import time
import logging
from seleniumbase import Driver

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_seleniumbase_uc():
    """测试SeleniumBase UC Mode"""
    driver = None
    try:
        logger.info("启动SeleniumBase UC Mode...")
        
        driver = Driver(
            uc=True,
            headless=True,
            incognito=True,
            do_not_track=True,
            undetectable=True,
        )
        
        logger.info("访问测试页面...")
        driver.get("https://httpbin.org/user-agent")
        
        time.sleep(2)
        
        page_source = driver.page_source
        logger.info(f"页面内容长度: {len(page_source)}")
        
        if "user-agent" in page_source.lower():
            logger.info("✅ SeleniumBase UC Mode 基础功能正常")
            
            # 测试supjav访问
            logger.info("测试supjav访问...")
            driver.get("https://supjav.com/357979.html")
            
            time.sleep(10)  # 等待Cloudflare挑战
            
            page_source = driver.page_source
            title = driver.title
            
            logger.info(f"页面标题: {title}")
            logger.info(f"页面内容长度: {len(page_source)}")
            
            if "just a moment" in page_source.lower():
                logger.warning("⚠️ 仍然遇到Cloudflare挑战")
            elif len(page_source) > 5000:
                logger.info("✅ 成功绕过Cloudflare保护")
            else:
                logger.warning("⚠️ 页面内容异常")
                
        else:
            logger.error("❌ SeleniumBase UC Mode 基础功能异常")
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
    finally:
        if driver:
            driver.quit()

def test_basic_selenium():
    """测试基础Selenium功能"""
    driver = None
    try:
        logger.info("启动基础Selenium...")
        
        driver = Driver(headless=True)
        
        logger.info("访问测试页面...")
        driver.get("https://httpbin.org/user-agent")
        
        time.sleep(2)
        
        page_source = driver.page_source
        logger.info(f"页面内容长度: {len(page_source)}")
        
        if "user-agent" in page_source.lower():
            logger.info("✅ 基础Selenium功能正常")
        else:
            logger.error("❌ 基础Selenium功能异常")
            
    except Exception as e:
        logger.error(f"基础测试失败: {str(e)}")
    finally:
        if driver:
            driver.quit()

def main():
    print("🧪 Cloudflare绕过功能测试")
    print("=" * 40)
    
    print("\n1. 测试基础Selenium功能...")
    test_basic_selenium()
    
    print("\n2. 测试SeleniumBase UC Mode...")
    test_seleniumbase_uc()
    
    print("\n" + "=" * 40)
    print("测试完成")

if __name__ == "__main__":
    main()
