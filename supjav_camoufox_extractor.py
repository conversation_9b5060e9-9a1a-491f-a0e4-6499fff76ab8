#!/usr/bin/env python3
"""
SupJAV Camoufox 2025最新提取器
使用Camoufox (Firefox基础) 绕过Cloudflare保护
"""

import re
import asyncio
import logging
from urllib.parse import urljoin
from camoufox.async_api import AsyncCamoufox

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavCamouFoxExtractor:
    def __init__(self):
        self.browser = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_browser()
        
    async def start_browser(self):
        """启动Camoufox浏览器"""
        try:
            logger.info("启动Camoufox浏览器...")
            
            # Camoufox 基础配置
            self.browser = await AsyncCamoufox(
                headless=True,  # 无头模式
            ).__aenter__()
            
            logger.info("Camoufox浏览器启动成功")
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {str(e)}")
            raise
    
    async def close_browser(self):
        """关闭浏览器"""
        if self.browser:
            try:
                await self.browser.__aexit__(None, None, None)
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {str(e)}")
    
    async def extract_video_url(self, page_url, max_retries=3):
        """
        从supjav页面提取视频直链
        
        Args:
            page_url (str): supjav页面URL
            max_retries (int): 最大重试次数
            
        Returns:
            dict: 包含视频信息的字典
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"开始提取视频链接 (尝试 {attempt + 1}/{max_retries}): {page_url}")
                
                # 创建新页面
                page = await self.browser.new_page()
                
                # 设置额外的反检测头
                await page.set_extra_http_headers({
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Cache-Control': 'max-age=0',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                })
                
                # 访问页面
                await page.goto(page_url, wait_until='networkidle')
                
                # 等待页面加载
                await page.wait_for_timeout(5000)
                
                # 检查是否遇到Cloudflare挑战
                content = await page.content()
                
                if "just a moment" in content.lower() or "checking your browser" in content.lower():
                    logger.info("检测到Cloudflare挑战，等待Camoufox自动解决...")
                    
                    # Camoufox基于Firefox，通常能更好地处理Cloudflare挑战
                    # 等待更长时间让挑战完成
                    await page.wait_for_timeout(20000)
                    
                    # 检查是否成功绕过
                    content = await page.content()
                    if "just a moment" in content.lower():
                        logger.warning(f"Cloudflare挑战未能自动解决 (尝试 {attempt + 1})")
                        await page.close()
                        if attempt < max_retries - 1:
                            await asyncio.sleep(5)  # 等待后重试
                            continue
                        else:
                            return None
                
                # 提取视频信息
                result = await self._extract_video_info(page, page_url)
                await page.close()
                
                if result:
                    logger.info("视频信息提取成功")
                    return result
                else:
                    logger.warning(f"未能提取到视频信息 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(3)
                        continue
                
            except Exception as e:
                logger.error(f"提取过程中出错 (尝试 {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
        
        logger.error("所有尝试都失败了")
        return None
    
    async def _extract_video_info(self, page, page_url):
        """从页面提取视频信息"""
        try:
            # 提取标题
            title = await self._extract_title(page)
            logger.info(f"视频标题: {title}")
            
            # 提取视频链接
            video_urls = []
            
            # 方法1: 查找video元素
            video_urls.extend(await self._extract_from_video_elements(page))
            
            # 方法2: 从页面源码分析
            content = await page.content()
            video_urls.extend(self._extract_from_page_source(content, page_url))
            
            # 方法3: 执行JavaScript获取播放器信息
            video_urls.extend(await self._extract_from_javascript(page))
            
            # 方法4: 查找iframe
            video_urls.extend(await self._extract_from_iframes(page, page_url))
            
            # 去重并验证
            unique_urls = list(dict.fromkeys(video_urls))
            valid_urls = []
            
            for url in unique_urls:
                if self._is_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到视频链接: {url}")
            
            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                # 输出页面内容片段用于调试
                logger.debug(f"页面内容片段: {content[:1000]}...")
                return None
                
            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }
            
        except Exception as e:
            logger.error(f"提取视频信息时出错: {str(e)}")
            return None
    
    async def _extract_title(self, page):
        """提取视频标题"""
        try:
            # 尝试多种选择器
            selectors = [
                "h1.entry-title",
                "h1",
                ".post-title",
                ".video-title"
            ]
            
            for selector in selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        title = await element.text_content()
                        if title:
                            # 清理标题
                            title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                            return title.strip()
                except:
                    continue
            
            # 从页面标题获取
            title = await page.title()
            if title:
                title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                return title.strip()
            
            return "Unknown Title"
            
        except Exception as e:
            logger.debug(f"提取标题失败: {str(e)}")
            return "Unknown Title"
    
    async def _extract_from_video_elements(self, page):
        """从video元素提取链接"""
        urls = []
        try:
            # 查找所有video元素
            video_elements = await page.query_selector_all("video")
            
            for video in video_elements:
                # 检查src属性
                src = await video.get_attribute("src")
                if src and self._is_video_url(src):
                    urls.append(src)
                
                # 检查source子元素
                sources = await video.query_selector_all("source")
                for source in sources:
                    src = await source.get_attribute("src")
                    if src and self._is_video_url(src):
                        urls.append(src)
            
        except Exception as e:
            logger.debug(f"从video元素提取链接失败: {str(e)}")
        
        return urls
    
    def _extract_from_page_source(self, page_source, base_url):
        """从页面源码提取视频链接"""
        urls = []
        
        # 视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'video\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    full_url = urljoin(base_url, match)
                    urls.append(full_url)
        
        return urls
    
    async def _extract_from_javascript(self, page):
        """通过JavaScript获取播放器信息"""
        urls = []
        
        try:
            # 尝试获取常见播放器的配置
            scripts = [
                "return window.jwplayer ? window.jwplayer().getConfig() : null;",
                "return window.videojs ? Object.values(window.videojs.getPlayers()).map(p => p.src()) : null;",
                "return window.player ? window.player.src() : null;",
                "return document.querySelector('video') ? document.querySelector('video').src : null;",
                "return window.playerConfig || window.config || null;"
            ]
            
            for script in scripts:
                try:
                    result = await page.evaluate(script)
                    if result:
                        logger.debug(f"JavaScript结果: {result}")
                        # 解析结果中的视频URL
                        if isinstance(result, str) and self._is_video_url(result):
                            urls.append(result)
                        elif isinstance(result, (dict, list)):
                            # 从配置对象中提取URL
                            self._extract_urls_from_config(result, urls)
                except Exception as e:
                    logger.debug(f"JavaScript执行失败: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.debug(f"JavaScript提取失败: {str(e)}")
        
        return urls
    
    def _extract_urls_from_config(self, config, urls):
        """从配置对象中提取URL"""
        if isinstance(config, dict):
            for key, value in config.items():
                if key in ['file', 'src', 'url'] and isinstance(value, str) and self._is_video_url(value):
                    urls.append(value)
                elif key == 'sources' and isinstance(value, list):
                    for source in value:
                        if isinstance(source, dict) and 'file' in source:
                            if self._is_video_url(source['file']):
                                urls.append(source['file'])
                        elif isinstance(source, str) and self._is_video_url(source):
                            urls.append(source)
                elif isinstance(value, (dict, list)):
                    self._extract_urls_from_config(value, urls)
        elif isinstance(config, list):
            for item in config:
                if isinstance(item, (dict, list)):
                    self._extract_urls_from_config(item, urls)
                elif isinstance(item, str) and self._is_video_url(item):
                    urls.append(item)
    
    async def _extract_from_iframes(self, page, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        try:
            iframes = await page.query_selector_all("iframe")
            
            for iframe in iframes:
                src = await iframe.get_attribute("src")
                if src and any(keyword in src.lower() for keyword in ['player', 'embed', 'video']):
                    logger.debug(f"发现iframe播放器: {src}")
                    # 可以进一步处理iframe内容
                    
        except Exception as e:
            logger.debug(f"从iframe提取失败: {str(e)}")
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = ['video', 'stream', 'play', 'media']
        return any(pattern in url.lower() for pattern in video_patterns)
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]

async def main():
    """测试函数"""
    async with SupJavCamouFoxExtractor() as extractor:
        test_url = "https://supjav.com/357979.html"
        
        result = await extractor.extract_video_url(test_url)
        if result:
            print(f"标题: {result['title']}")
            print(f"最佳链接: {result['best_url']}")
            print(f"所有链接: {result['video_urls']}")
        else:
            print("提取失败")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
