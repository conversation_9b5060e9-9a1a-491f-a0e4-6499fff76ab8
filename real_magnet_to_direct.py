#!/usr/bin/env python3
"""
真正的磁力链接转直链工具
使用实际可用的API获取真实下载链接
"""

import requests
import time
import re
import json
import sys
import subprocess
from urllib.parse import quote, unquote
import tempfile
import os

class RealMagnetConverter:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    def extract_magnet_info(self, magnet_url):
        """提取磁力链接信息"""
        info = {}
        
        # 提取hash
        hash_match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
        if hash_match:
            info['hash'] = hash_match.group(1)
        
        # 提取显示名称
        dn_match = re.search(r'dn=([^&]+)', magnet_url)
        if dn_match:
            info['name'] = unquote(dn_match.group(1))
        
        return info
    
    def try_webtor_real_api(self, magnet_url):
        """尝试webtor.io的真实API"""
        try:
            print("🔄 尝试webtor.io API...")
            
            # 方法1: 尝试直接API调用
            api_url = "https://webtor.io/api/add"
            data = {
                'magnet': magnet_url
            }
            
            # 先获取页面获取CSRF token
            page_response = self.session.get("https://webtor.io")
            
            if page_response.status_code == 200:
                # 查找CSRF token
                csrf_match = re.search(r'csrf["\']?\s*:\s*["\']([^"\']+)', page_response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"📋 获取到CSRF token: {csrf_token[:20]}...")
                    
                    headers = {
                        'X-CSRF-Token': csrf_token,
                        'Content-Type': 'application/json',
                        'Referer': 'https://webtor.io'
                    }
                    
                    response = self.session.post(api_url, json=data, headers=headers)
                    
                    if response.status_code == 200:
                        result = response.json()
                        print("✅ webtor.io API调用成功")
                        return {'success': True, 'data': result}
                    else:
                        print(f"❌ API调用失败: {response.status_code}")
                        print(f"响应: {response.text[:200]}")
            
            return {'success': False, 'error': 'API调用失败'}
            
        except Exception as e:
            print(f"❌ webtor.io API错误: {e}")
            return {'success': False, 'error': str(e)}
    
    def try_local_torrent_processing(self, magnet_url):
        """使用本地工具处理torrent"""
        try:
            print("🔄 尝试本地torrent处理...")
            
            magnet_info = self.extract_magnet_info(magnet_url)
            torrent_hash = magnet_info.get('hash')
            
            if not torrent_hash:
                return {'success': False, 'error': '无法提取torrent hash'}
            
            # 使用aria2c获取torrent文件
            with tempfile.TemporaryDirectory() as temp_dir:
                print("📥 正在获取torrent元数据...")
                
                cmd = [
                    'aria2c',
                    '--bt-metadata-only=true',
                    '--bt-save-metadata=true',
                    '--dir=' + temp_dir,
                    '--quiet=true',
                    magnet_url
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # 查找生成的torrent文件
                    torrent_files = []
                    for file in os.listdir(temp_dir):
                        if file.endswith('.torrent'):
                            torrent_files.append(os.path.join(temp_dir, file))
                    
                    if torrent_files:
                        torrent_file = torrent_files[0]
                        print(f"✅ 成功获取torrent文件: {os.path.basename(torrent_file)}")
                        
                        # 解析torrent文件获取文件列表
                        file_info = self.parse_torrent_file(torrent_file)
                        
                        return {
                            'success': True,
                            'torrent_file': torrent_file,
                            'files': file_info,
                            'hash': torrent_hash
                        }
                    else:
                        return {'success': False, 'error': '未找到torrent文件'}
                else:
                    return {'success': False, 'error': f'aria2c失败: {result.stderr}'}
                    
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': '获取torrent元数据超时'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def parse_torrent_file(self, torrent_file):
        """解析torrent文件获取文件信息"""
        try:
            # 使用aria2c显示torrent信息
            cmd = ['aria2c', '--show-files=true', torrent_file]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            files = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if '|' in line and 'idx' not in line.lower():
                        parts = line.split('|')
                        if len(parts) >= 3:
                            try:
                                idx = parts[0].strip()
                                size = parts[1].strip()
                                path = parts[2].strip()
                                files.append({
                                    'index': idx,
                                    'size': size,
                                    'path': path
                                })
                            except:
                                continue
            
            return files
            
        except Exception as e:
            print(f"❌ 解析torrent文件失败: {e}")
            return []
    
    def try_alternative_apis(self, magnet_url):
        """尝试其他可用的API"""
        try:
            print("🔄 尝试其他API服务...")
            
            # 尝试一些公开的torrent API
            apis = [
                {
                    'name': 'TorrentAPI',
                    'url': 'https://torrentapi.org/pubapi_v2.php',
                    'method': 'get_token'
                }
            ]
            
            results = []
            
            for api in apis:
                try:
                    print(f"📡 尝试 {api['name']}...")
                    
                    # 这里可以添加具体的API调用逻辑
                    # 由于大多数API需要认证，这里只是示例
                    
                    results.append({
                        'service': api['name'],
                        'status': 'needs_auth',
                        'message': '需要API密钥或认证'
                    })
                    
                except Exception as e:
                    print(f"❌ {api['name']} 失败: {e}")
            
            return results
            
        except Exception as e:
            print(f"❌ 其他API尝试失败: {e}")
            return []
    
    def create_http_server_for_files(self, torrent_result):
        """为torrent文件创建HTTP服务器"""
        try:
            if not torrent_result.get('success'):
                return {'success': False, 'error': '无有效的torrent数据'}
            
            print("🌐 创建本地HTTP服务器...")
            
            # 这里可以实现一个简单的HTTP服务器
            # 提供torrent文件的直接下载
            
            files = torrent_result.get('files', [])
            if files:
                print("📁 可下载的文件:")
                for i, file_info in enumerate(files, 1):
                    print(f"{i}. {file_info.get('path', 'Unknown')} ({file_info.get('size', 'Unknown')})")
                
                # 生成aria2c下载命令
                torrent_file = torrent_result.get('torrent_file')
                if torrent_file:
                    print(f"\n🔧 使用aria2c下载命令:")
                    print(f"aria2c '{torrent_file}'")
                    
                    return {
                        'success': True,
                        'method': 'local_download',
                        'torrent_file': torrent_file,
                        'files': files
                    }
            
            return {'success': False, 'error': '无可用文件'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_magnet_link(self, magnet_url):
        """处理磁力链接"""
        print("🧲 真正的磁力链接转直链工具")
        print("=" * 60)
        
        magnet_info = self.extract_magnet_info(magnet_url)
        
        if magnet_info.get('hash'):
            print(f"🔑 Hash: {magnet_info['hash']}")
        if magnet_info.get('name'):
            print(f"📁 名称: {magnet_info['name']}")
        
        print(f"📋 磁力链接: {magnet_url[:80]}...")
        
        # 尝试各种方法获取真实下载链接
        methods = [
            ('webtor.io API', self.try_webtor_real_api),
            ('本地torrent处理', self.try_local_torrent_processing),
        ]
        
        successful_results = []
        
        for method_name, method_func in methods:
            print(f"\n🔄 尝试方法: {method_name}")
            result = method_func(magnet_url)
            
            if result.get('success'):
                print(f"✅ {method_name} 成功!")
                successful_results.append((method_name, result))
                
                # 如果是本地处理成功，创建下载方案
                if method_name == '本地torrent处理':
                    server_result = self.create_http_server_for_files(result)
                    if server_result.get('success'):
                        successful_results.append(('本地下载服务', server_result))
                
                break  # 找到一个成功的方法就停止
            else:
                print(f"❌ {method_name} 失败: {result.get('error', '未知错误')}")
        
        # 显示结果
        if successful_results:
            print("\n🎯 获取到的真实下载方案:")
            print("-" * 50)
            
            for method_name, result in successful_results:
                print(f"📦 方法: {method_name}")
                
                if method_name == '本地下载服务':
                    files = result.get('files', [])
                    torrent_file = result.get('torrent_file', '')
                    
                    print(f"   📄 Torrent文件: {torrent_file}")
                    print(f"   📁 包含 {len(files)} 个文件")
                    
                    if files:
                        print("   📋 文件列表:")
                        for i, file_info in enumerate(files[:5], 1):  # 只显示前5个
                            print(f"      {i}. {file_info.get('path', 'Unknown')}")
                        if len(files) > 5:
                            print(f"      ... 还有 {len(files) - 5} 个文件")
                    
                    print(f"   🔧 下载命令: aria2c '{torrent_file}'")
                
                print()
        else:
            print("\n❌ 所有方法都失败了")
            print("💡 建议:")
            print("1. 检查磁力链接是否有效")
            print("2. 确保网络连接正常")
            print("3. 尝试使用传统的BitTorrent客户端")
        
        return successful_results

def main():
    if len(sys.argv) != 2:
        print("用法: python3 real_magnet_to_direct.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    if not magnet_url.startswith('magnet:'):
        print("❌ 请提供有效的磁力链接")
        sys.exit(1)
    
    converter = RealMagnetConverter()
    converter.process_magnet_link(magnet_url)

if __name__ == "__main__":
    main()