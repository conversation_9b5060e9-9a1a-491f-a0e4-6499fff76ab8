#!/usr/bin/env python3
"""
测试浏览器是否能正常工作
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_browser():
    try:
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        logger.info("正在启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        logger.info("访问测试页面...")
        driver.get("https://httpbin.org/user-agent")
        
        page_source = driver.page_source
        logger.info(f"页面内容: {page_source[:200]}...")
        
        driver.quit()
        logger.info("浏览器测试成功!")
        return True
        
    except Exception as e:
        logger.error(f"浏览器测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_browser()
