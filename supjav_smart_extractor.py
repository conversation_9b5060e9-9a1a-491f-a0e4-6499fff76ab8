#!/usr/bin/env python3
"""
SupJAV智能提取器
自动选择最佳的反爬虫绕过方案
"""

import sys
import time
import logging
import asyncio
import requests
from typing import Optional, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavSmartExtractor:
    def __init__(self):
        self.methods = [
            ("NoDriver", self._try_nodriver),
            ("SeleniumBase UC", self._try_seleniumbase),
            ("FlareSolverr", self._try_flaresolverr),
        ]
        
    async def extract_video_url(self, page_url: str) -> Optional[Dict[str, Any]]:
        """
        智能提取视频URL，自动尝试多种方法
        
        Args:
            page_url (str): supjav页面URL
            
        Returns:
            dict: 包含视频信息的字典
        """
        logger.info(f"开始智能提取视频链接: {page_url}")
        
        for method_name, method_func in self.methods:
            try:
                logger.info(f"尝试方法: {method_name}")
                
                result = await method_func(page_url)
                
                if result:
                    logger.info(f"✅ {method_name} 成功提取到视频信息")
                    result['extraction_method'] = method_name
                    return result
                else:
                    logger.warning(f"❌ {method_name} 未能提取到视频信息")
                    
            except Exception as e:
                logger.error(f"❌ {method_name} 执行失败: {str(e)}")
                continue
        
        logger.error("所有方法都失败了")
        return None
    
    async def _try_nodriver(self, page_url: str) -> Optional[Dict[str, Any]]:
        """尝试NoDriver方法"""
        try:
            from supjav_nodriver_extractor import SupJavNoDriverExtractor
            
            async with SupJavNoDriverExtractor() as extractor:
                return await extractor.extract_video_url(page_url, max_retries=2)
                
        except ImportError:
            logger.error("NoDriver未安装，请运行: pip install nodriver")
            return None
        except Exception as e:
            logger.error(f"NoDriver方法失败: {str(e)}")
            return None
    
    async def _try_seleniumbase(self, page_url: str) -> Optional[Dict[str, Any]]:
        """尝试SeleniumBase UC Mode方法"""
        try:
            from supjav_seleniumbase_extractor import SupJavSeleniumBaseExtractor
            
            # 在异步环境中运行同步代码
            def run_seleniumbase():
                with SupJavSeleniumBaseExtractor() as extractor:
                    return extractor.extract_video_url(page_url, max_retries=2)
            
            # 使用线程池执行同步代码
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_seleniumbase)
                return future.result(timeout=120)  # 2分钟超时
                
        except ImportError:
            logger.error("SeleniumBase未安装，请运行: pip install seleniumbase")
            return None
        except Exception as e:
            logger.error(f"SeleniumBase方法失败: {str(e)}")
            return None
    
    async def _try_flaresolverr(self, page_url: str) -> Optional[Dict[str, Any]]:
        """尝试FlareSolverr方法"""
        try:
            # 检查FlareSolverr是否运行
            try:
                response = requests.get("http://localhost:8191/v1", timeout=5)
                if response.status_code != 200:
                    logger.warning("FlareSolverr服务未运行，跳过此方法")
                    return None
            except requests.exceptions.RequestException:
                logger.warning("无法连接到FlareSolverr服务，跳过此方法")
                return None
            
            from supjav_flaresolverr_extractor import SupJavFlareSolverrExtractor
            
            # 在异步环境中运行同步代码
            def run_flaresolverr():
                with SupJavFlareSolverrExtractor() as extractor:
                    return extractor.extract_video_url(page_url, max_retries=2)
            
            # 使用线程池执行同步代码
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_flaresolverr)
                return future.result(timeout=180)  # 3分钟超时
                
        except ImportError:
            logger.error("FlareSolverr提取器导入失败")
            return None
        except Exception as e:
            logger.error(f"FlareSolverr方法失败: {str(e)}")
            return None

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python supjav_smart_extractor.py <supjav_url>")
        print("示例: python supjav_smart_extractor.py https://supjav.com/357979.html")
        sys.exit(1)
    
    page_url = sys.argv[1]
    
    extractor = SupJavSmartExtractor()
    
    print("🚀 SupJAV智能提取器启动")
    print("=" * 50)
    
    start_time = time.time()
    result = await extractor.extract_video_url(page_url)
    end_time = time.time()
    
    print("\n" + "=" * 50)
    print("📊 提取结果:")
    print("=" * 50)
    
    if result:
        print(f"✅ 提取成功!")
        print(f"🔧 使用方法: {result['extraction_method']}")
        print(f"📝 视频标题: {result['title']}")
        print(f"🎬 最佳链接: {result['best_url']}")
        print(f"📋 所有链接: {len(result['video_urls'])} 个")
        
        for i, url in enumerate(result['video_urls'], 1):
            print(f"   {i}. {url}")
        
        print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
        
        # 如果需要，可以直接上传到StreamHG
        upload_choice = input("\n是否上传到StreamHG? (y/N): ").strip().lower()
        if upload_choice == 'y':
            await upload_to_streamhg(result)
    else:
        print("❌ 提取失败")
        print("💡 建议:")
        print("   1. 检查URL是否正确")
        print("   2. 确保网络连接正常")
        print("   3. 尝试启动FlareSolverr服务")
        print("   4. 检查依赖是否完整安装")
    
    print("=" * 50)

async def upload_to_streamhg(video_info: Dict[str, Any]):
    """上传到StreamHG"""
    try:
        from supjav_to_streamhg import SupJavToStreamHG
        import json
        
        # 加载配置
        try:
            with open('streamhg_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
        except FileNotFoundError:
            print("❌ 未找到StreamHG配置文件")
            return
        
        print("\n🚀 开始上传到StreamHG...")
        
        # 创建上传器
        uploader = SupJavToStreamHG(config['streamhg_api_key'])
        
        # 上传最佳视频链接
        upload_result = uploader.uploader.upload_by_url(
            video_info['best_url'],
            title=video_info['title']
        )
        
        if upload_result['success']:
            print(f"✅ 上传成功!")
            print(f"📁 StreamHG链接: {upload_result['url']}")
            print(f"🔑 文件代码: {upload_result['file_code']}")
        else:
            print(f"❌ 上传失败: {upload_result['message']}")
            
    except ImportError:
        print("❌ StreamHG上传模块未找到")
    except Exception as e:
        print(f"❌ 上传过程中出错: {str(e)}")

if __name__ == "__main__":
    # 运行异步主函数
    try:
        import nodriver as uc
        uc.loop().run_until_complete(main())
    except ImportError:
        # 如果nodriver未安装，使用标准asyncio
        asyncio.run(main())
