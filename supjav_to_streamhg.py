#!/usr/bin/env python3
"""
SupJAV到StreamHG远程下载工具
从supjav.com提取视频链接并通过StreamHG远程下载
"""

import os
import sys
import time
import json
import logging
import requests
from urllib.parse import urlparse
from supjav_extractor import SupJavExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StreamHGUploader:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://streamhgapi.com/api"
        
    def upload_by_url(self, video_url, title=None, folder_id=None):
        """
        通过URL上传视频到StreamHG
        
        Args:
            video_url (str): 视频直链
            title (str): 视频标题
            folder_id (int): 文件夹ID
            
        Returns:
            dict: 上传结果
        """
        try:
            logger.info(f"开始上传视频到StreamHG: {video_url}")
            
            # 构建请求参数
            params = {
                'key': self.api_key,
                'url': video_url
            }
            
            if folder_id:
                params['fld_id'] = folder_id
            
            if title:
                params['file_title'] = title
            
            # 发送上传请求
            response = requests.get(f"{self.base_url}/upload/url", params=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('status') == 200:
                file_code = result.get('result', {}).get('filecode')
                if file_code:
                    logger.info(f"上传成功，文件代码: {file_code}")
                    return {
                        'success': True,
                        'file_code': file_code,
                        'url': f"https://streamhg.com/{file_code}",
                        'message': '上传成功'
                    }
            
            logger.error(f"上传失败: {result}")
            return {
                'success': False,
                'message': result.get('msg', '未知错误')
            }
            
        except Exception as e:
            logger.error(f"上传过程中发生错误: {str(e)}")
            return {
                'success': False,
                'message': str(e)
            }
    
    def check_upload_status(self, file_code):
        """
        检查上传状态
        
        Args:
            file_code (str): 文件代码
            
        Returns:
            dict: 文件信息
        """
        try:
            params = {
                'key': self.api_key,
                'file_code': file_code
            }
            
            response = requests.get(f"{self.base_url}/file/info", params=params, timeout=15)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('status') == 200:
                file_info = result.get('result', [])
                if file_info:
                    return file_info[0]
            
            return None
            
        except Exception as e:
            logger.error(f"检查上传状态失败: {str(e)}")
            return None

class SupJavToStreamHG:
    def __init__(self, streamhg_api_key):
        self.extractor = SupJavExtractor()
        self.uploader = StreamHGUploader(streamhg_api_key)
        
    def process_url(self, supjav_url, folder_id=None, wait_for_completion=True):
        """
        处理supjav URL，提取视频并上传到StreamHG
        
        Args:
            supjav_url (str): supjav页面URL
            folder_id (int): StreamHG文件夹ID
            wait_for_completion (bool): 是否等待上传完成
            
        Returns:
            dict: 处理结果
        """
        try:
            logger.info(f"开始处理SupJAV URL: {supjav_url}")
            
            # 步骤1: 提取视频信息
            video_info = self.extractor.extract_video_url(supjav_url)
            if not video_info:
                return {
                    'success': False,
                    'message': '无法从SupJAV页面提取视频链接'
                }
            
            logger.info(f"成功提取视频信息: {video_info['title']}")
            
            # 步骤2: 上传到StreamHG
            upload_result = self.uploader.upload_by_url(
                video_info['best_url'],
                title=video_info['title'],
                folder_id=folder_id
            )
            
            if not upload_result['success']:
                return upload_result
            
            result = {
                'success': True,
                'supjav_url': supjav_url,
                'video_title': video_info['title'],
                'video_url': video_info['best_url'],
                'streamhg_code': upload_result['file_code'],
                'streamhg_url': upload_result['url'],
                'message': '处理成功'
            }
            
            # 步骤3: 等待上传完成（可选）
            if wait_for_completion:
                logger.info("等待StreamHG处理完成...")
                result['upload_status'] = self._wait_for_upload_completion(upload_result['file_code'])
            
            return result
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {str(e)}")
            return {
                'success': False,
                'message': str(e)
            }
    
    def _wait_for_upload_completion(self, file_code, max_wait_time=300):
        """
        等待上传完成
        
        Args:
            file_code (str): 文件代码
            max_wait_time (int): 最大等待时间（秒）
            
        Returns:
            dict: 最终状态
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            file_info = self.uploader.check_upload_status(file_code)
            
            if file_info:
                status = file_info.get('status', 0)
                if status == 200:
                    logger.info("上传处理完成")
                    return {
                        'completed': True,
                        'file_info': file_info
                    }
                else:
                    logger.info(f"上传处理中，状态: {status}")
            
            time.sleep(10)  # 等待10秒后再次检查
        
        logger.warning("等待上传完成超时")
        return {
            'completed': False,
            'message': '等待超时'
        }
    
    def batch_process(self, urls, folder_id=None):
        """
        批量处理多个URL
        
        Args:
            urls (list): supjav URL列表
            folder_id (int): StreamHG文件夹ID
            
        Returns:
            list: 处理结果列表
        """
        results = []
        
        for i, url in enumerate(urls, 1):
            logger.info(f"处理第 {i}/{len(urls)} 个URL")
            
            result = self.process_url(url, folder_id, wait_for_completion=False)
            results.append(result)
            
            # 添加延迟避免请求过快
            if i < len(urls):
                time.sleep(5)
        
        return results

def load_config():
    """加载配置文件"""
    config_file = 'streamhg_config.json'
    
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        # 创建默认配置文件
        default_config = {
            'streamhg_api_key': '28820wijqa8yocnc3c9u',
            'default_folder_id': None,
            'wait_for_completion': True
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"已创建默认配置文件: {config_file}")
        return default_config

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python supjav_to_streamhg.py <supjav_url> [folder_id]")
        print("示例: python supjav_to_streamhg.py https://supjav.com/357979.html")
        sys.exit(1)
    
    # 加载配置
    config = load_config()
    
    # 获取参数
    supjav_url = sys.argv[1]
    folder_id = int(sys.argv[2]) if len(sys.argv) > 2 else config.get('default_folder_id')
    
    # 创建处理器
    processor = SupJavToStreamHG(config['streamhg_api_key'])
    
    # 处理URL
    result = processor.process_url(
        supjav_url, 
        folder_id=folder_id,
        wait_for_completion=config.get('wait_for_completion', True)
    )
    
    # 输出结果
    print("\n" + "="*50)
    print("处理结果:")
    print("="*50)
    
    if result['success']:
        print(f"✅ 成功!")
        print(f"视频标题: {result['video_title']}")
        print(f"StreamHG链接: {result['streamhg_url']}")
        print(f"文件代码: {result['streamhg_code']}")
        
        if 'upload_status' in result:
            if result['upload_status']['completed']:
                print("✅ 上传处理完成")
            else:
                print("⏳ 上传仍在处理中")
    else:
        print(f"❌ 失败: {result['message']}")
    
    print("="*50)

if __name__ == "__main__":
    main()
