#!/usr/bin/env python3
"""
SupJAV SeleniumBase UC Mode提取器
使用SeleniumBase的UC模式绕过Cloudflare保护
"""

import re
import time
import logging
from urllib.parse import urljoin
from seleniumbase import Driver

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SupJavSeleniumBaseExtractor:
    def __init__(self):
        self.driver = None
        
    def __enter__(self):
        """上下文管理器入口"""
        self.start_driver()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_driver()
        
    def start_driver(self):
        """启动SeleniumBase UC Mode驱动"""
        try:
            logger.info("启动SeleniumBase UC Mode驱动...")
            
            # UC Mode配置 - 专门用于绕过反爬虫
            self.driver = Driver(
                uc=True,  # 启用UC模式
                headless=True,  # 无头模式
                incognito=True,  # 隐身模式
                do_not_track=True,  # 启用Do Not Track
                undetectable=True,  # 启用不可检测模式
                # 其他反检测选项
                disable_csp=True,  # 禁用内容安全策略
                disable_ws=True,  # 禁用WebSocket
                block_images=True,  # 阻止图片加载以提高速度
            )
            
            logger.info("SeleniumBase UC Mode驱动启动成功")
            
        except Exception as e:
            logger.error(f"启动驱动失败: {str(e)}")
            raise
    
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("驱动已关闭")
            except Exception as e:
                logger.error(f"关闭驱动时出错: {str(e)}")
    
    def extract_video_url(self, page_url, max_retries=3):
        """
        从supjav页面提取视频直链
        
        Args:
            page_url (str): supjav页面URL
            max_retries (int): 最大重试次数
            
        Returns:
            dict: 包含视频信息的字典
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"开始提取视频链接 (尝试 {attempt + 1}/{max_retries}): {page_url}")
                
                # 访问页面
                self.driver.get(page_url)
                
                # 等待页面加载
                time.sleep(3)
                
                # 检查是否遇到Cloudflare挑战
                page_source = self.driver.page_source
                
                if "just a moment" in page_source.lower() or "checking your browser" in page_source.lower():
                    logger.info("检测到Cloudflare挑战，等待UC Mode自动解决...")
                    
                    # UC Mode通常能自动处理Cloudflare挑战
                    # 等待更长时间让挑战完成
                    time.sleep(15)
                    
                    # 检查是否成功绕过
                    page_source = self.driver.page_source
                    if "just a moment" in page_source.lower():
                        logger.warning(f"Cloudflare挑战未能自动解决 (尝试 {attempt + 1})")
                        if attempt < max_retries - 1:
                            time.sleep(5)  # 等待后重试
                            continue
                        else:
                            return None
                
                # 提取视频信息
                result = self._extract_video_info(page_url)
                
                if result:
                    logger.info("视频信息提取成功")
                    return result
                else:
                    logger.warning(f"未能提取到视频信息 (尝试 {attempt + 1})")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                
            except Exception as e:
                logger.error(f"提取过程中出错 (尝试 {attempt + 1}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue
        
        logger.error("所有尝试都失败了")
        return None
    
    def _extract_video_info(self, page_url):
        """从页面提取视频信息"""
        try:
            # 提取标题
            title = self._extract_title()
            logger.info(f"视频标题: {title}")
            
            # 提取视频链接
            video_urls = []
            
            # 方法1: 查找video元素
            video_urls.extend(self._extract_from_video_elements())
            
            # 方法2: 从页面源码分析
            page_source = self.driver.page_source
            video_urls.extend(self._extract_from_page_source(page_source, page_url))
            
            # 方法3: 执行JavaScript获取播放器信息
            video_urls.extend(self._extract_from_javascript())
            
            # 方法4: 查找iframe
            video_urls.extend(self._extract_from_iframes(page_url))
            
            # 去重并验证
            unique_urls = list(dict.fromkeys(video_urls))
            valid_urls = []
            
            for url in unique_urls:
                if self._is_video_url(url):
                    valid_urls.append(url)
                    logger.info(f"找到视频链接: {url}")
            
            if not valid_urls:
                logger.warning("未找到有效的视频链接")
                return None
                
            return {
                'title': title,
                'page_url': page_url,
                'video_urls': valid_urls,
                'best_url': self._select_best_url(valid_urls)
            }
            
        except Exception as e:
            logger.error(f"提取视频信息时出错: {str(e)}")
            return None
    
    def _extract_title(self):
        """提取视频标题"""
        try:
            # 尝试多种选择器
            selectors = [
                "h1.entry-title",
                "h1",
                ".post-title",
                ".video-title"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements("css selector", selector)
                    if elements:
                        title = elements[0].text.strip()
                        if title:
                            # 清理标题
                            title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                            return title
                except:
                    continue
            
            # 从页面标题获取
            title = self.driver.title
            if title:
                title = re.sub(r'\s*-\s*SupJAV.*$', '', title, flags=re.IGNORECASE)
                return title.strip()
            
            return "Unknown Title"
            
        except Exception as e:
            logger.debug(f"提取标题失败: {str(e)}")
            return "Unknown Title"
    
    def _extract_from_video_elements(self):
        """从video元素提取链接"""
        urls = []
        try:
            # 查找所有video元素
            video_elements = self.driver.find_elements("tag name", "video")
            
            for video in video_elements:
                # 检查src属性
                src = video.get_attribute("src")
                if src and self._is_video_url(src):
                    urls.append(src)
                
                # 检查source子元素
                sources = video.find_elements("tag name", "source")
                for source in sources:
                    src = source.get_attribute("src")
                    if src and self._is_video_url(src):
                        urls.append(src)
            
        except Exception as e:
            logger.debug(f"从video元素提取链接失败: {str(e)}")
        
        return urls
    
    def _extract_from_page_source(self, page_source, base_url):
        """从页面源码提取视频链接"""
        urls = []
        
        # 视频URL模式
        patterns = [
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.webm[^"\']*)["\']',
            r'src\s*:\s*["\']([^"\']+)["\']',
            r'file\s*:\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if self._is_video_url(match):
                    full_url = urljoin(base_url, match)
                    urls.append(full_url)
        
        return urls
    
    def _extract_from_javascript(self):
        """通过JavaScript获取播放器信息"""
        urls = []
        
        try:
            # 尝试获取常见播放器的配置
            scripts = [
                "return window.jwplayer ? window.jwplayer().getConfig() : null;",
                "return window.videojs ? Object.values(window.videojs.getPlayers()).map(p => p.src()) : null;",
                "return window.player ? window.player.src() : null;",
                "return document.querySelector('video') ? document.querySelector('video').src : null;",
                "return window.playerConfig || window.config || null;"
            ]
            
            for script in scripts:
                try:
                    result = self.driver.execute_script(script)
                    if result:
                        logger.debug(f"JavaScript结果: {result}")
                        # 解析结果中的视频URL
                        if isinstance(result, str) and self._is_video_url(result):
                            urls.append(result)
                        elif isinstance(result, (dict, list)):
                            # 从配置对象中提取URL
                            self._extract_urls_from_config(result, urls)
                except Exception as e:
                    logger.debug(f"JavaScript执行失败: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.debug(f"JavaScript提取失败: {str(e)}")
        
        return urls
    
    def _extract_urls_from_config(self, config, urls):
        """从配置对象中提取URL"""
        if isinstance(config, dict):
            for key, value in config.items():
                if key in ['file', 'src', 'url'] and isinstance(value, str) and self._is_video_url(value):
                    urls.append(value)
                elif key == 'sources' and isinstance(value, list):
                    for source in value:
                        if isinstance(source, dict) and 'file' in source:
                            if self._is_video_url(source['file']):
                                urls.append(source['file'])
                        elif isinstance(source, str) and self._is_video_url(source):
                            urls.append(source)
                elif isinstance(value, (dict, list)):
                    self._extract_urls_from_config(value, urls)
        elif isinstance(config, list):
            for item in config:
                if isinstance(item, (dict, list)):
                    self._extract_urls_from_config(item, urls)
                elif isinstance(item, str) and self._is_video_url(item):
                    urls.append(item)
    
    def _extract_from_iframes(self, base_url):
        """从iframe中提取视频链接"""
        urls = []
        
        try:
            iframes = self.driver.find_elements("tag name", "iframe")
            
            for iframe in iframes:
                src = iframe.get_attribute("src")
                if src and any(keyword in src.lower() for keyword in ['player', 'embed', 'video']):
                    logger.debug(f"发现iframe播放器: {src}")
                    # 这里可以进一步处理iframe内容
                    
        except Exception as e:
            logger.debug(f"从iframe提取失败: {str(e)}")
        
        return urls
    
    def _is_video_url(self, url):
        """检查URL是否可能是视频链接"""
        if not url or len(url) < 10:
            return False
        
        # 检查文件扩展名
        video_extensions = ['.mp4', '.m3u8', '.webm', '.avi', '.mkv', '.mov', '.flv']
        if any(ext in url.lower() for ext in video_extensions):
            return True
        
        # 检查URL模式
        video_patterns = ['video', 'stream', 'play', 'media']
        return any(pattern in url.lower() for pattern in video_patterns)
    
    def _select_best_url(self, urls):
        """选择最佳的视频URL"""
        if not urls:
            return None
        
        # 优先级：mp4 > m3u8 > 其他
        mp4_urls = [url for url in urls if '.mp4' in url.lower()]
        if mp4_urls:
            return mp4_urls[0]
        
        m3u8_urls = [url for url in urls if '.m3u8' in url.lower()]
        if m3u8_urls:
            return m3u8_urls[0]
        
        return urls[0]

def main():
    """测试函数"""
    with SupJavSeleniumBaseExtractor() as extractor:
        test_url = "https://supjav.com/357979.html"
        
        result = extractor.extract_video_url(test_url)
        if result:
            print(f"标题: {result['title']}")
            print(f"最佳链接: {result['best_url']}")
            print(f"所有链接: {result['video_urls']}")
        else:
            print("提取失败")

if __name__ == "__main__":
    main()
