#!/usr/bin/env python3
"""
磁力链接剪贴板监听器
自动检测剪贴板中的磁力链接并转换为直链
"""

import subprocess
import time
import re
import sys
import os
from urllib.parse import quote, unquote

class MagnetClipboardMonitor:
    def __init__(self):
        self.last_clipboard = ""
        
    def get_clipboard_content(self):
        """获取剪贴板内容"""
        try:
            env = {'DISPLAY': ':1'}
            result = subprocess.run(['xclip', '-selection', 'clipboard', '-o'], 
                                  capture_output=True, text=True, env=env)
            return result.stdout.strip()
        except Exception as e:
            print(f"❌ 获取剪贴板内容失败: {e}")
            return ""
    
    def is_magnet_link(self, text):
        """检查是否为磁力链接"""
        return bool(re.match(r'^magnet:\?xt=urn:btih:[a-fA-F0-9]{40}', text))
    
    def process_magnet_link(self, magnet_url):
        """处理磁力链接"""
        print("\n" + "=" * 60)
        print("🧲 检测到磁力链接!")
        print("=" * 60)
        
        # 提取信息
        hash_match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
        name_match = re.search(r'dn=([^&]+)', magnet_url)
        
        if hash_match:
            torrent_hash = hash_match.group(1)
            print(f"🔑 Hash: {torrent_hash}")
        
        if name_match:
            name = unquote(name_match.group(1))
            print(f"📁 名称: {name}")
        
        print(f"📋 磁力链接: {magnet_url[:80]}...")
        
        # 生成在线服务链接
        services = [
            ("Webtor.io", f"https://webtor.io/?magnet={quote(magnet_url)}", "在线播放和下载"),
            ("Instant.io", f"https://instant.io/#{magnet_url}", "即时下载和流媒体"),
            ("Seedr.cc", f"https://www.seedr.cc/?magnet={quote(magnet_url)}", "云端下载服务"),
            ("Put.io", f"https://put.io/transfers/add?url={quote(magnet_url)}", "云存储下载")
        ]
        
        if hash_match:
            services.append(("BTCache.me", f"https://btcache.me/torrent/{torrent_hash}", "Torrent缓存和下载"))
        
        print("\n🎯 可用的在线服务:")
        print("-" * 50)
        
        for i, (name, url, desc) in enumerate(services, 1):
            print(f"{i}. {name}")
            print(f"   🌐 {url}")
            print(f"   📝 {desc}")
            print()
        
        # 自动复制第一个链接到剪贴板
        if services:
            first_url = services[0][1]
            try:
                env = {'DISPLAY': ':1'}
                subprocess.run(['xclip', '-selection', 'clipboard'], 
                             input=first_url, text=True, env=env)
                print(f"📋 已将 {services[0][0]} 链接复制到剪贴板")
            except Exception as e:
                print(f"⚠️  复制到剪贴板失败: {e}")
        
        print("\n📖 使用方法:")
        print("1. 上面的链接已复制到剪贴板，直接粘贴到浏览器")
        print("2. 或者手动复制其他服务的链接")
        print("3. 在浏览器中打开链接")
        print("4. 等待解析完成后下载文件")
        
        return True
    
    def monitor_clipboard(self):
        """监听剪贴板变化"""
        print("🎯 磁力链接剪贴板监听器")
        print("=" * 40)
        print("📋 正在监听剪贴板中的磁力链接...")
        print("💡 复制磁力链接到剪贴板即可自动处理")
        print("🛑 按 Ctrl+C 退出")
        print("-" * 40)
        
        while True:
            try:
                current_clipboard = self.get_clipboard_content()
                
                if current_clipboard and current_clipboard != self.last_clipboard:
                    if self.is_magnet_link(current_clipboard):
                        self.process_magnet_link(current_clipboard)
                        print("\n" + "-" * 40)
                        print("⏳ 等待下一个磁力链接...")
                    
                    self.last_clipboard = current_clipboard
                
                time.sleep(1)  # 每秒检查一次
                
            except KeyboardInterrupt:
                print("\n👋 监听器已退出")
                break
            except Exception as e:
                print(f"❌ 监听过程中出错: {e}")
                time.sleep(5)

def main():
    # 检查xclip是否安装
    try:
        subprocess.run(['which', 'xclip'], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("❌ 未找到 xclip，正在安装...")
        try:
            subprocess.run(['apt', 'install', '-y', 'xclip'], check=True)
        except Exception as e:
            print(f"❌ 安装 xclip 失败: {e}")
            sys.exit(1)
    
    monitor = MagnetClipboardMonitor()
    
    if len(sys.argv) > 1:
        # 直接处理命令行参数中的磁力链接
        magnet_url = sys.argv[1]
        if not monitor.is_magnet_link(magnet_url):
            print("❌ 请提供有效的磁力链接")
            sys.exit(1)
        
        monitor.process_magnet_link(magnet_url)
    else:
        # 监听剪贴板模式
        monitor.monitor_clipboard()

if __name__ == "__main__":
    main()