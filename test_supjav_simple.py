#!/usr/bin/env python3
"""
简单测试supjav页面访问
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_supjav_access():
    driver = None
    try:
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        logger.info("正在启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        test_url = "https://supjav.com/357979.html"
        logger.info(f"访问supjav页面: {test_url}")
        
        driver.get(test_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 获取页面标题
        title = driver.title
        logger.info(f"页面标题: {title}")
        
        # 检查页面是否被阻止
        page_source = driver.page_source
        
        if "403" in page_source or "Forbidden" in page_source:
            logger.warning("页面返回403错误")
        elif "cloudflare" in page_source.lower():
            logger.warning("检测到Cloudflare保护")
        elif len(page_source) < 1000:
            logger.warning(f"页面内容过短: {len(page_source)} 字符")
        else:
            logger.info(f"页面加载成功，内容长度: {len(page_source)} 字符")
            
            # 查找视频相关元素
            try:
                video_elements = driver.find_elements(By.TAG_NAME, "video")
                logger.info(f"找到 {len(video_elements)} 个video元素")
                
                iframe_elements = driver.find_elements(By.TAG_NAME, "iframe")
                logger.info(f"找到 {len(iframe_elements)} 个iframe元素")
                
                # 查找可能的播放器容器
                player_containers = driver.find_elements(By.CSS_SELECTOR, "[id*='player'], [class*='player'], [id*='video'], [class*='video']")
                logger.info(f"找到 {len(player_containers)} 个播放器容器")
                
                # 输出页面的一部分内容用于调试
                logger.info("页面内容片段:")
                logger.info(page_source[:500] + "...")
                
            except Exception as e:
                logger.error(f"查找元素时出错: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"访问supjav失败: {str(e)}")
        return False
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    test_supjav_access()
