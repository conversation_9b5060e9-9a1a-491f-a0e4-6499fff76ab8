#!/usr/bin/env python3
"""
实用的磁力链接解决方案
提供真正可行的下载方法
"""

import subprocess
import re
import sys
import os
import time
from urllib.parse import unquote

class PracticalMagnetSolution:
    def __init__(self):
        pass
    
    def extract_magnet_info(self, magnet_url):
        """提取磁力链接信息"""
        info = {}
        
        # 提取hash
        hash_match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
        if hash_match:
            info['hash'] = hash_match.group(1)
        
        # 提取显示名称
        dn_match = re.search(r'dn=([^&]+)', magnet_url)
        if dn_match:
            info['name'] = unquote(dn_match.group(1))
        
        # 提取tracker
        tr_matches = re.findall(r'tr=([^&]+)', magnet_url)
        if tr_matches:
            info['trackers'] = [unquote(tr) for tr in tr_matches]
        
        return info
    
    def setup_aria2_download(self, magnet_url, download_dir="/www/wwwroot/JAVAPI.COM/downloads"):
        """使用aria2设置下载"""
        try:
            print("🔧 设置aria2下载...")
            
            # 确保下载目录存在
            os.makedirs(download_dir, exist_ok=True)
            
            # aria2配置
            aria2_cmd = [
                'aria2c',
                '--dir=' + download_dir,
                '--max-connection-per-server=16',
                '--max-concurrent-downloads=16',
                '--split=16',
                '--min-split-size=1M',
                '--bt-max-peers=100',
                '--seed-time=0',  # 下载完成后不做种
                '--bt-tracker-connect-timeout=10',
                '--bt-tracker-timeout=10',
                '--follow-torrent=mem',
                '--enable-dht=true',
                '--bt-enable-lpd=true',
                '--enable-peer-exchange=true',
                magnet_url
            ]
            
            print(f"📁 下载目录: {download_dir}")
            print("🚀 启动aria2下载...")
            print("命令:", ' '.join(aria2_cmd))
            
            # 启动下载
            process = subprocess.Popen(aria2_cmd, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE,
                                     text=True)
            
            return {
                'success': True,
                'process': process,
                'download_dir': download_dir,
                'method': 'aria2c直接下载'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def setup_docker_aria2_download(self, magnet_url):
        """使用Docker中的aria2下载"""
        try:
            print("🐳 使用Docker aria2下载...")
            
            download_dir = "/www/wwwroot/JAVAPI.COM/downloads"
            os.makedirs(download_dir, exist_ok=True)
            
            # Docker aria2命令
            docker_cmd = [
                'docker', 'run', '--rm',
                '-v', f'{download_dir}:/downloads',
                '-p', '6800:6800',
                'aria2pro/aria2-pro:latest',
                '--dir=/downloads',
                '--enable-rpc=true',
                '--rpc-listen-all=true',
                '--rpc-allow-origin-all=true',
                '--max-connection-per-server=16',
                '--bt-max-peers=100',
                '--seed-time=0',
                magnet_url
            ]
            
            print("🚀 启动Docker aria2...")
            process = subprocess.Popen(docker_cmd,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     text=True)
            
            return {
                'success': True,
                'process': process,
                'download_dir': download_dir,
                'method': 'Docker aria2下载'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_download_script(self, magnet_url, magnet_info):
        """创建下载脚本"""
        try:
            script_path = "/www/wwwroot/JAVAPI.COM/download_magnet.sh"
            
            script_content = f"""#!/bin/bash
# 磁力链接下载脚本
# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

MAGNET_URL="{magnet_url}"
DOWNLOAD_DIR="/www/wwwroot/JAVAPI.COM/downloads"
HASH="{magnet_info.get('hash', 'unknown')}"
NAME="{magnet_info.get('name', 'unknown')}"

echo "🧲 磁力链接下载脚本"
echo "=================================="
echo "📁 名称: $NAME"
echo "🔑 Hash: $HASH"
echo "📂 下载目录: $DOWNLOAD_DIR"
echo "=================================="

# 创建下载目录
mkdir -p "$DOWNLOAD_DIR"

# 方法1: 使用aria2c直接下载
echo "🚀 方法1: aria2c直接下载"
aria2c \\
    --dir="$DOWNLOAD_DIR" \\
    --max-connection-per-server=16 \\
    --max-concurrent-downloads=16 \\
    --split=16 \\
    --min-split-size=1M \\
    --bt-max-peers=100 \\
    --seed-time=0 \\
    --bt-tracker-connect-timeout=10 \\
    --bt-tracker-timeout=10 \\
    --follow-torrent=mem \\
    --enable-dht=true \\
    --bt-enable-lpd=true \\
    --enable-peer-exchange=true \\
    --continue=true \\
    --summary-interval=60 \\
    "$MAGNET_URL"

echo "✅ 下载完成!"
echo "📁 文件位置: $DOWNLOAD_DIR"
ls -la "$DOWNLOAD_DIR"
"""
            
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            os.chmod(script_path, 0o755)
            
            return {
                'success': True,
                'script_path': script_path,
                'method': '下载脚本'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def provide_manual_solutions(self, magnet_url, magnet_info):
        """提供手动解决方案"""
        solutions = []
        
        # 解决方案1: 直接使用aria2c
        solutions.append({
            'name': 'aria2c命令行下载',
            'description': '使用aria2c直接下载到本地',
            'command': f'aria2c --dir=/www/wwwroot/JAVAPI.COM/downloads --seed-time=0 "{magnet_url}"',
            'pros': ['速度快', '支持断点续传', '资源占用少'],
            'cons': ['需要有种子', '依赖网络环境']
        })
        
        # 解决方案2: 使用qBittorrent
        solutions.append({
            'name': 'qBittorrent Web UI',
            'description': '安装qBittorrent并使用Web界面管理下载',
            'command': 'apt install qbittorrent-nox && qbittorrent-nox',
            'pros': ['图形界面', '功能完整', '支持RSS'],
            'cons': ['占用资源较多', '需要配置']
        })
        
        # 解决方案3: 使用Transmission
        solutions.append({
            'name': 'Transmission Daemon',
            'description': '轻量级BitTorrent客户端',
            'command': 'apt install transmission-daemon && systemctl start transmission-daemon',
            'pros': ['轻量级', 'Web界面', '稳定'],
            'cons': ['功能相对简单']
        })
        
        return solutions
    
    def process_magnet_link(self, magnet_url):
        """处理磁力链接"""
        print("🧲 实用磁力链接解决方案")
        print("=" * 60)
        
        magnet_info = self.extract_magnet_info(magnet_url)
        
        if magnet_info.get('hash'):
            print(f"🔑 Hash: {magnet_info['hash']}")
        if magnet_info.get('name'):
            print(f"📁 名称: {magnet_info['name']}")
        if magnet_info.get('trackers'):
            print(f"📡 Tracker数量: {len(magnet_info['trackers'])}")
        
        print(f"📋 磁力链接: {magnet_url[:80]}...")
        
        # 创建下载脚本
        script_result = self.create_download_script(magnet_url, magnet_info)
        if script_result.get('success'):
            print(f"\n✅ 已创建下载脚本: {script_result['script_path']}")
            print(f"🔧 运行命令: bash {script_result['script_path']}")
        
        # 提供手动解决方案
        solutions = self.provide_manual_solutions(magnet_url, magnet_info)
        
        print("\n🎯 可用的下载方案:")
        print("-" * 50)
        
        for i, solution in enumerate(solutions, 1):
            print(f"{i}. {solution['name']}")
            print(f"   📝 说明: {solution['description']}")
            print(f"   🔧 命令: {solution['command']}")
            print(f"   ✅ 优点: {', '.join(solution['pros'])}")
            print(f"   ⚠️  缺点: {', '.join(solution['cons'])}")
            print()
        
        # 提供即时下载选项
        print("🚀 即时下载选项:")
        print("-" * 30)
        print("1. 运行下载脚本:")
        print(f"   bash {script_result.get('script_path', '/path/to/script')}")
        print()
        print("2. 直接使用aria2c:")
        print(f"   aria2c --dir=/www/wwwroot/JAVAPI.COM/downloads --seed-time=0 \"{magnet_url}\"")
        print()
        print("3. 后台下载:")
        print(f"   nohup aria2c --dir=/www/wwwroot/JAVAPI.COM/downloads --seed-time=0 \"{magnet_url}\" > /tmp/download.log 2>&1 &")
        
        return True

def main():
    if len(sys.argv) != 2:
        print("用法: python3 practical_magnet_solution.py <磁力链接>")
        print("示例: python3 practical_magnet_solution.py 'magnet:?xt=urn:btih:...'")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    if not magnet_url.startswith('magnet:'):
        print("❌ 请提供有效的磁力链接")
        sys.exit(1)
    
    solution = PracticalMagnetSolution()
    solution.process_magnet_link(magnet_url)

if __name__ == "__main__":
    main()